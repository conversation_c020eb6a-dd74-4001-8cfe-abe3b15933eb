/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #2c5aa0;
}

.nav-brand .logo {
    height: 40px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu li {
    margin-left: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 10px 0;
}

.nav-link:hover,
.nav-link.active {
    color: #2c5aa0;
}

.login-btn {
    background: #2c5aa0;
    color: #fff !important;
    padding: 10px 20px !important;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.login-btn:hover {
    background: #1e3d6f;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* 首页轮播图样式 */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
    margin-top: 70px;
}

.hero-slider {
    position: relative;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
}

.slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: brightness(0.7);
}

.slide-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #fff;
    max-width: 800px;
    padding: 0 20px;
}

.slide-content h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.slide-content p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.cta-button {
    display: inline-block;
    background: #2c5aa0;
    color: #fff;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.cta-button:hover {
    background: #1e3d6f;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
}

.slider-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.prev-btn {
    position: absolute;
    left: 30px;
}

.next-btn {
    position: absolute;
    right: 30px;
}

.prev-btn,
.next-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.5);
    color: #fff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.prev-btn:hover,
.next-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.8);
}

.slider-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: #fff;
}

/* 通用区块样式 */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* 产品展示样式 */
.products-section {
    background: #f8f9fa;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.product-card {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.product-card.featured::before {
    content: '推荐';
    position: absolute;
    top: 15px;
    right: -25px;
    background: #ff6b6b;
    color: #fff;
    padding: 5px 30px;
    font-size: 12px;
    transform: rotate(45deg);
}

.product-icon {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 20px;
}

.product-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 15px;
}

.product-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.product-price {
    font-size: 1.8rem;
    color: #2c5aa0;
    font-weight: bold;
    margin-bottom: 20px;
}

.product-price .currency {
    font-size: 1rem;
}

.product-price .period {
    font-size: 0.9rem;
    color: #999;
}

.view-all-btn {
    display: inline-block;
    background: transparent;
    color: #2c5aa0;
    border: 2px solid #2c5aa0;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background: #2c5aa0;
    color: #fff;
}

.text-center {
    text-align: center;
}

/* 公司优势样式 */
.advantages-section {
    background: #fff;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.advantage-item {
    text-align: center;
    padding: 20px;
}

.advantage-icon {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 20px;
}

.advantage-item h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 15px;
}

.advantage-item p {
    color: #666;
    line-height: 1.6;
}

/* 关于我们样式 */
.about-section {
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
}

.about-text p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.8;
}

.company-stats {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #2c5aa0;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.about-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* 联系我们样式 */
.contact-section {
    background: #fff;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.contact-icon {
    font-size: 2rem;
    color: #2c5aa0;
    margin-top: 5px;
}

.contact-details h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 10px;
}

.contact-details p {
    color: #666;
    margin-bottom: 5px;
}

.contact-form {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 10px;
}

.contact-form h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c5aa0;
}

.submit-btn {
    width: 100%;
    background: #2c5aa0;
    color: #fff;
    border: none;
    padding: 15px;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.submit-btn:hover {
    background: #1e3d6f;
}

/* 页脚样式 */
.footer {
    background: #2c3e50;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: #fff;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fff;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.social-link {
    display: inline-block;
    padding: 8px 15px;
    background: #34495e;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.social-link:hover {
    background: #2c5aa0;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 20px;
    text-align: center;
    color: #bdc3c7;
}

.footer-bottom a {
    color: #bdc3c7;
    text-decoration: none;
}

.footer-bottom a:hover {
    color: #fff;
}

/* 图标字体样式 */
[class^="icon-"]:before {
    font-family: 'iconfont';
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-left: .2em;
}

/* 图标内容 */
.icon-security:before { content: '🔒'; }
.icon-performance:before { content: '⚡'; }
.icon-support:before { content: '🎧'; }
.icon-flexible:before { content: '📈'; }
.icon-phone:before { content: '📞'; }
.icon-email:before { content: '✉️'; }
.icon-location:before { content: '📍'; }
.icon-server:before { content: '🖥️'; }
.icon-hosting:before { content: '🌐'; }
.icon-apps:before { content: '📱'; }
.icon-domain:before { content: '🌍'; }
