# 图片资源目录

本目录用于存放网站所需的图片资源。

## 目录结构

```
images/
├── logo/                   # 网站Logo
│   ├── logo.png           # 主Logo
│   ├── logo-white.png     # 白色Logo（深色背景用）
│   └── favicon.ico        # 网站图标
├── hero/                  # 首页轮播图
│   ├── slide1.jpg         # 轮播图1
│   ├── slide2.jpg         # 轮播图2
│   └── slide3.jpg         # 轮播图3
├── products/              # 产品相关图片
│   ├── cloud-server.jpg   # 云服务器
│   ├── virtual-host.jpg   # 虚拟主机
│   ├── cloud-apps.jpg     # 云应用
│   └── domain.jpg         # 域名服务
├── about/                 # 关于我们页面图片
│   ├── company.jpg        # 公司图片
│   ├── team/              # 团队成员照片
│   │   ├── ceo.jpg
│   │   ├── cto.jpg
│   │   └── manager.jpg
│   └── office.jpg         # 办公环境
├── icons/                 # 图标文件
│   ├── security.svg       # 安全图标
│   ├── performance.svg    # 性能图标
│   ├── support.svg        # 支持图标
│   └── flexible.svg       # 灵活图标
└── backgrounds/           # 背景图片
    ├── hero-bg.jpg        # 首页背景
    ├── section-bg.jpg     # 区块背景
    └── footer-bg.jpg      # 页脚背景
```

## 图片规格建议

### Logo
- 主Logo: 200x60px, PNG格式，透明背景
- 白色Logo: 200x60px, PNG格式，透明背景
- Favicon: 32x32px, ICO格式

### 轮播图
- 尺寸: 1920x800px
- 格式: JPG
- 质量: 80-90%
- 文件大小: <500KB

### 产品图片
- 尺寸: 400x300px
- 格式: JPG
- 质量: 80%
- 文件大小: <100KB

### 团队照片
- 尺寸: 300x300px
- 格式: JPG
- 质量: 85%
- 文件大小: <80KB

### 图标
- 格式: SVG（矢量图标）
- 备用格式: PNG 64x64px

## 图片优化建议

1. **压缩优化**: 使用工具如TinyPNG、ImageOptim等压缩图片
2. **响应式图片**: 为不同屏幕尺寸准备多个版本
3. **WebP格式**: 现代浏览器支持WebP格式，文件更小
4. **懒加载**: 对非首屏图片使用懒加载技术
5. **CDN加速**: 将图片部署到CDN提高加载速度

## 使用说明

在HTML中引用图片时，使用相对路径：

```html
<!-- Logo -->
<img src="images/logo/logo.png" alt="幻方清云">

<!-- 轮播图 -->
<div class="slide-bg" style="background-image: url('images/hero/slide1.jpg')"></div>

<!-- 产品图片 -->
<img src="images/products/cloud-server.jpg" alt="云服务器">
```

在CSS中使用背景图片：

```css
.hero-section {
    background-image: url('../images/backgrounds/hero-bg.jpg');
}
```

## 版权说明

- 所有图片应确保有合法使用权
- 建议使用免费商用图片网站：
  - Unsplash (https://unsplash.com/)
  - Pexels (https://www.pexels.com/)
  - Pixabay (https://pixabay.com/)
- 或购买正版图片素材

## 更新日志

- 2024-01-01: 创建图片目录结构
- 待添加: 实际图片文件上传
