// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initNavigation();
    initHeroSlider();
    initProductsLoader();
    initContactForm();
    initScrollEffects();
    initFAQ();
});

// 导航栏功能
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // 移动端菜单切换
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
    
    // 点击导航链接关闭移动端菜单
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navMenu.classList.contains('active')) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });
    
    // 滚动时导航栏样式变化
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.backdropFilter = 'blur(10px)';
        } else {
            navbar.style.background = '#fff';
            navbar.style.backdropFilter = 'none';
        }
    });
    
    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 70; // 减去导航栏高度
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// 轮播图功能
function initHeroSlider() {
    const slides = document.querySelectorAll('.slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');
    
    if (slides.length === 0) return;
    
    let currentSlide = 0;
    const totalSlides = slides.length;
    
    // 显示指定幻灯片
    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
        });
        
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });
        
        currentSlide = index;
    }
    
    // 下一张幻灯片
    function nextSlide() {
        const next = (currentSlide + 1) % totalSlides;
        showSlide(next);
    }
    
    // 上一张幻灯片
    function prevSlide() {
        const prev = (currentSlide - 1 + totalSlides) % totalSlides;
        showSlide(prev);
    }
    
    // 绑定按钮事件
    if (nextBtn) nextBtn.addEventListener('click', nextSlide);
    if (prevBtn) prevBtn.addEventListener('click', prevSlide);
    
    // 绑定圆点事件
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
    });
    
    // 自动播放
    let autoPlayInterval = setInterval(nextSlide, 5000);
    
    // 鼠标悬停时暂停自动播放
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroSection.addEventListener('mouseenter', () => {
            clearInterval(autoPlayInterval);
        });
        
        heroSection.addEventListener('mouseleave', () => {
            autoPlayInterval = setInterval(nextSlide, 5000);
        });
    }
    
    // 键盘控制
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') prevSlide();
        if (e.key === 'ArrowRight') nextSlide();
    });
}

// 产品数据加载
function initProductsLoader() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;
    
    // 从API加载产品数据
    fetch('../api.ly-y.cn/products.json')
        .then(response => response.json())
        .then(data => {
            displayProducts(data.products, productsGrid);
        })
        .catch(error => {
            console.error('加载产品数据失败:', error);
            displayErrorMessage(productsGrid);
        });
}

// 显示产品
function displayProducts(products, container) {
    const featuredProducts = [];
    
    // 获取推荐产品
    Object.keys(products).forEach(key => {
        const product = products[key];
        if (product.featured) {
            featuredProducts.push({
                key: key,
                ...product
            });
        }
    });
    
    // 如果没有推荐产品，显示前3个
    if (featuredProducts.length === 0) {
        Object.keys(products).slice(0, 3).forEach(key => {
            featuredProducts.push({
                key: key,
                ...products[key]
            });
        });
    }
    
    // 生成产品卡片HTML
    container.innerHTML = featuredProducts.map(product => {
        const plan = product.plans[0]; // 显示第一个套餐
        return `
            <div class="product-card ${product.featured ? 'featured' : ''}">
                <div class="product-icon">
                    <i class="${product.icon}"></i>
                </div>
                <h3>${product.title}</h3>
                <p>${product.description}</p>
                <div class="product-price">
                    <span class="currency">¥</span>${plan.price.monthly}
                    <span class="period">/月</span>
                </div>
                <a href="products.html#${product.key}" class="cta-button">了解详情</a>
            </div>
        `;
    }).join('');
}

// 显示错误信息
function displayErrorMessage(container) {
    container.innerHTML = `
        <div class="error-message" style="text-align: center; padding: 40px; color: #666;">
            <p>暂时无法加载产品信息，请稍后再试。</p>
            <p>如有疑问，请联系客服：************</p>
        </div>
    `;
}

// 联系表单功能
function initContactForm() {
    const contactForm = document.getElementById('contact-form');
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取表单数据
        const formData = new FormData(contactForm);
        const data = Object.fromEntries(formData);
        
        // 验证表单
        if (!validateContactForm(data)) {
            return;
        }
        
        // 显示提交状态
        const submitBtn = contactForm.querySelector('.submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '提交中...';
        submitBtn.disabled = true;
        
        // 模拟提交（实际项目中应该发送到后端API）
        setTimeout(() => {
            alert('感谢您的咨询！我们会尽快与您联系。');
            contactForm.reset();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
}

// 验证联系表单
function validateContactForm(data) {
    const errors = [];
    
    if (!data.name || data.name.trim().length < 2) {
        errors.push('请输入有效的姓名');
    }
    
    if (!data.email || !isValidEmail(data.email)) {
        errors.push('请输入有效的邮箱地址');
    }
    
    if (!data.message || data.message.trim().length < 10) {
        errors.push('请输入至少10个字符的详细描述');
    }
    
    if (errors.length > 0) {
        alert('请修正以下错误：\n' + errors.join('\n'));
        return false;
    }
    
    return true;
}

// 验证邮箱格式
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 滚动效果
function initScrollEffects() {
    // 滚动时显示元素动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animateElements = document.querySelectorAll('.product-card, .advantage-item, .contact-item');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
    
    // 回到顶部按钮
    const backToTop = createBackToTopButton();
    document.body.appendChild(backToTop);
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 500) {
            backToTop.style.display = 'block';
        } else {
            backToTop.style.display = 'none';
        }
    });
}

// 创建回到顶部按钮
function createBackToTopButton() {
    const button = document.createElement('button');
    button.innerHTML = '↑';
    button.className = 'back-to-top';
    button.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: #2c5aa0;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 20px;
        cursor: pointer;
        display: none;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
    `;
    
    button.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    button.addEventListener('mouseenter', function() {
        this.style.background = '#1e3d6f';
        this.style.transform = 'translateY(-2px)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.background = '#2c5aa0';
        this.style.transform = 'translateY(0)';
    });
    
    return button;
}

// FAQ功能
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const toggle = item.querySelector('.faq-toggle');
        
        if (question && answer && toggle) {
            // 初始状态
            answer.style.display = 'none';
            
            question.addEventListener('click', function() {
                const isOpen = answer.style.display === 'block';
                
                // 关闭所有其他FAQ
                faqItems.forEach(otherItem => {
                    const otherAnswer = otherItem.querySelector('.faq-answer');
                    const otherToggle = otherItem.querySelector('.faq-toggle');
                    if (otherAnswer && otherToggle) {
                        otherAnswer.style.display = 'none';
                        otherToggle.textContent = '+';
                    }
                });
                
                // 切换当前FAQ
                if (!isOpen) {
                    answer.style.display = 'block';
                    toggle.textContent = '-';
                } else {
                    answer.style.display = 'none';
                    toggle.textContent = '+';
                }
            });
        }
    });
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
