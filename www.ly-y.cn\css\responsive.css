/* 响应式设计 */

/* 平板设备 */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .slide-content h1 {
        font-size: 2.8rem;
    }
    
    .slide-content p {
        font-size: 1.1rem;
    }
    
    .about-content {
        gap: 40px;
    }
    
    .contact-content {
        gap: 40px;
    }
    
    .company-stats {
        gap: 20px;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    /* 导航栏响应式 */
    .navbar .container {
        padding: 0 15px;
    }
    
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: #fff;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: left 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 20px 0;
    }
    
    .nav-link {
        font-size: 1.2rem;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    /* 轮播图响应式 */
    .hero-section {
        height: 70vh;
        min-height: 500px;
    }
    
    .slide-content {
        padding: 0 15px;
    }
    
    .slide-content h1 {
        font-size: 2.2rem;
        margin-bottom: 15px;
    }
    
    .slide-content p {
        font-size: 1rem;
        margin-bottom: 25px;
    }
    
    .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
    
    .prev-btn,
    .next-btn {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .prev-btn {
        left: 15px;
    }
    
    .next-btn {
        right: 15px;
    }
    
    .slider-dots {
        bottom: 20px;
    }
    
    .dot {
        width: 10px;
        height: 10px;
    }
    
    /* 区块响应式 */
    section {
        padding: 60px 0;
    }
    
    .section-header {
        margin-bottom: 40px;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .section-header p {
        font-size: 1rem;
    }
    
    /* 产品网格响应式 */
    .products-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .product-card {
        padding: 25px;
    }
    
    .product-icon {
        font-size: 2.5rem;
    }
    
    .product-card h3 {
        font-size: 1.3rem;
    }
    
    .product-price {
        font-size: 1.5rem;
    }
    
    /* 优势网格响应式 */
    .advantages-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .advantage-icon {
        font-size: 2.5rem;
    }
    
    /* 关于我们响应式 */
    .about-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }
    
    .about-text {
        order: 2;
    }
    
    .about-image {
        order: 1;
    }
    
    .company-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .stat-item {
        min-width: 80px;
    }
    
    .stat-number {
        font-size: 1.3rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    /* 联系我们响应式 */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .contact-info {
        gap: 25px;
    }
    
    .contact-item {
        gap: 15px;
    }
    
    .contact-icon {
        font-size: 1.5rem;
    }
    
    .contact-details h4 {
        font-size: 1.1rem;
    }
    
    .contact-form {
        padding: 30px 20px;
    }
    
    .contact-form h3 {
        font-size: 1.3rem;
        margin-bottom: 25px;
    }
    
    /* 页脚响应式 */
    .footer {
        padding: 40px 0 15px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-bottom {
        font-size: 0.9rem;
    }
}

/* 小屏手机设备 */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .navbar .container {
        padding: 0 10px;
    }
    
    .nav-brand {
        font-size: 20px;
    }
    
    .nav-brand .logo {
        height: 35px;
    }
    
    .slide-content h1 {
        font-size: 1.8rem;
    }
    
    .slide-content p {
        font-size: 0.9rem;
    }
    
    .cta-button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .section-header h2 {
        font-size: 1.7rem;
    }
    
    .product-card {
        padding: 20px;
    }
    
    .product-icon {
        font-size: 2rem;
    }
    
    .product-card h3 {
        font-size: 1.2rem;
    }
    
    .product-price {
        font-size: 1.3rem;
    }
    
    .advantage-icon {
        font-size: 2rem;
    }
    
    .advantage-item h3 {
        font-size: 1.1rem;
    }
    
    .contact-form {
        padding: 25px 15px;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 10px;
        font-size: 0.9rem;
    }
    
    .submit-btn {
        padding: 12px;
        font-size: 1rem;
    }
    
    .company-stats {
        gap: 10px;
    }
    
    .stat-number {
        font-size: 1.1rem;
    }
    
    .stat-label {
        font-size: 0.7rem;
    }
}

/* 超宽屏设备 */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .slide-content h1 {
        font-size: 4rem;
    }
    
    .slide-content p {
        font-size: 1.4rem;
    }
    
    .section-header h2 {
        font-size: 3rem;
    }
    
    .section-header p {
        font-size: 1.2rem;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .hero-section,
    .footer {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    section {
        padding: 20px 0;
        page-break-inside: avoid;
    }
    
    .section-header h2 {
        font-size: 18pt;
        color: #000;
    }
    
    .product-card,
    .advantage-item,
    .contact-item {
        border: 1px solid #ddd;
        padding: 15px;
        margin-bottom: 15px;
        page-break-inside: avoid;
    }
    
    a {
        color: #000;
        text-decoration: underline;
    }
    
    .cta-button,
    .view-all-btn,
    .submit-btn {
        border: 2px solid #000;
        background: none;
        color: #000;
    }
}
