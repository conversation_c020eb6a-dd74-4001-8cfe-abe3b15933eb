// 产品页面专用JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initProductsPage();
    initProductTabs();
    initProductComparison();
});

// 初始化产品页面
function initProductsPage() {
    loadAllProducts();
}

// 加载所有产品
function loadAllProducts() {
    const productsGrid = document.getElementById('all-products-grid');
    if (!productsGrid) return;
    
    fetch('../api.ly-y.cn/products.json')
        .then(response => response.json())
        .then(data => {
            displayAllProducts(data.products, productsGrid);
            initProductFiltering(data.products);
        })
        .catch(error => {
            console.error('加载产品数据失败:', error);
            displayErrorMessage(productsGrid);
        });
}

// 显示所有产品
function displayAllProducts(products, container) {
    const allProducts = [];
    
    // 展开所有产品和套餐
    Object.keys(products).forEach(key => {
        const product = products[key];
        product.plans.forEach(plan => {
            allProducts.push({
                productKey: key,
                category: product.category,
                productTitle: product.title,
                productIcon: product.icon,
                ...plan
            });
        });
    });
    
    // 生成产品卡片HTML
    container.innerHTML = allProducts.map(product => {
        return createProductCard(product);
    }).join('');
    
    // 添加购买按钮事件
    addPurchaseEvents();
}

// 创建产品卡片
function createProductCard(product) {
    const monthlyPrice = product.price.monthly || 0;
    const yearlyPrice = product.price.yearly || 0;
    const features = product.features || [];
    
    return `
        <div class="product-card ${product.popular ? 'popular' : ''}" data-category="${product.productKey}">
            ${product.popular ? '<div class="popular-badge">热门推荐</div>' : ''}
            <div class="product-header">
                <div class="product-icon">
                    <i class="${product.productIcon}"></i>
                </div>
                <h3>${product.name}</h3>
                <p class="product-category">${product.category}</p>
                <p class="product-description">${product.description}</p>
            </div>
            
            <div class="product-specs">
                ${product.cpu ? `<div class="spec-item"><span>CPU:</span> ${product.cpu}</div>` : ''}
                ${product.memory ? `<div class="spec-item"><span>内存:</span> ${product.memory}</div>` : ''}
                ${product.storage ? `<div class="spec-item"><span>存储:</span> ${product.storage}</div>` : ''}
                ${product.bandwidth ? `<div class="spec-item"><span>带宽:</span> ${product.bandwidth}</div>` : ''}
                ${product.domains ? `<div class="spec-item"><span>域名:</span> ${product.domains}</div>` : ''}
                ${product.databases ? `<div class="spec-item"><span>数据库:</span> ${product.databases}</div>` : ''}
                ${product.visits ? `<div class="spec-item"><span>访问量:</span> ${product.visits}</div>` : ''}
            </div>
            
            <div class="product-pricing">
                ${monthlyPrice > 0 ? `
                    <div class="price-option">
                        <span class="price-label">月付</span>
                        <span class="price-value">¥${monthlyPrice}/月</span>
                    </div>
                ` : ''}
                ${yearlyPrice > 0 ? `
                    <div class="price-option ${monthlyPrice > 0 ? 'recommended' : ''}">
                        <span class="price-label">年付</span>
                        <span class="price-value">¥${yearlyPrice}/年</span>
                        ${monthlyPrice > 0 ? `<span class="price-save">省¥${(monthlyPrice * 12 - yearlyPrice)}</span>` : ''}
                    </div>
                ` : ''}
            </div>
            
            <div class="product-features">
                <h4>功能特性</h4>
                <ul>
                    ${features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
            </div>
            
            <div class="product-actions">
                <button class="btn-purchase" data-product-id="${product.id}" data-product-name="${product.name}">
                    立即购买
                </button>
                <button class="btn-compare" data-product-id="${product.id}">
                    加入对比
                </button>
            </div>
        </div>
    `;
}

// 产品分类标签功能
function initProductTabs() {
    const tabLinks = document.querySelectorAll('.tab-link');
    const productCards = document.querySelectorAll('.product-card');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 更新活动标签
            tabLinks.forEach(tab => tab.classList.remove('active'));
            this.classList.add('active');
            
            // 过滤产品
            const category = this.getAttribute('data-category');
            filterProducts(category, productCards);
        });
    });
}

// 过滤产品
function filterProducts(category, productCards) {
    productCards.forEach(card => {
        if (category === 'all' || card.getAttribute('data-category') === category) {
            card.style.display = 'block';
            card.style.animation = 'fadeIn 0.5s ease';
        } else {
            card.style.display = 'none';
        }
    });
}

// 初始化产品过滤
function initProductFiltering(products) {
    // 根据URL哈希显示特定分类
    const hash = window.location.hash.substring(1);
    if (hash && products[hash]) {
        const tabLink = document.querySelector(`[data-category="${hash}"]`);
        if (tabLink) {
            tabLink.click();
        }
    }
}

// 产品对比功能
function initProductComparison() {
    let comparisonList = [];
    const maxComparison = 3;
    
    // 监听对比按钮点击
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-compare')) {
            const productId = e.target.getAttribute('data-product-id');
            toggleComparison(productId, e.target);
        }
    });
    
    function toggleComparison(productId, button) {
        const index = comparisonList.indexOf(productId);
        
        if (index > -1) {
            // 移除对比
            comparisonList.splice(index, 1);
            button.textContent = '加入对比';
            button.classList.remove('active');
        } else {
            // 添加对比
            if (comparisonList.length >= maxComparison) {
                alert(`最多只能对比${maxComparison}个产品`);
                return;
            }
            
            comparisonList.push(productId);
            button.textContent = '移除对比';
            button.classList.add('active');
        }
        
        updateComparisonTable();
    }
    
    function updateComparisonTable() {
        const comparisonTable = document.getElementById('comparison-table');
        if (!comparisonTable) return;
        
        if (comparisonList.length === 0) {
            comparisonTable.innerHTML = '<p class="no-comparison">请选择要对比的产品</p>';
            return;
        }
        
        // 这里应该根据实际产品数据生成对比表格
        comparisonTable.innerHTML = `
            <div class="comparison-header">
                <h3>产品对比 (${comparisonList.length}/${maxComparison})</h3>
                <button class="clear-comparison">清空对比</button>
            </div>
            <div class="comparison-content">
                <p>已选择 ${comparisonList.length} 个产品进行对比</p>
                <p>产品ID: ${comparisonList.join(', ')}</p>
            </div>
        `;
        
        // 绑定清空对比事件
        const clearBtn = comparisonTable.querySelector('.clear-comparison');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                comparisonList = [];
                document.querySelectorAll('.btn-compare.active').forEach(btn => {
                    btn.textContent = '加入对比';
                    btn.classList.remove('active');
                });
                updateComparisonTable();
            });
        }
    }
}

// 添加购买按钮事件
function addPurchaseEvents() {
    document.querySelectorAll('.btn-purchase').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            
            // 检查用户是否已登录
            if (!isUserLoggedIn()) {
                if (confirm(`购买 ${productName} 需要先登录，是否前往登录页面？`)) {
                    window.location.href = 'https://auth.ly-y.cn?redirect=' + encodeURIComponent(window.location.href);
                }
                return;
            }
            
            // 跳转到购买页面
            window.location.href = `https://app.ly-y.cn/purchase?product=${productId}`;
        });
    });
}

// 检查用户登录状态（模拟）
function isUserLoggedIn() {
    // 这里应该检查实际的登录状态
    // 可以通过cookie、localStorage或API调用来检查
    return localStorage.getItem('user_token') !== null;
}

// 显示错误信息
function displayErrorMessage(container) {
    container.innerHTML = `
        <div class="error-message">
            <div class="error-icon">⚠️</div>
            <h3>加载失败</h3>
            <p>暂时无法加载产品信息，请稍后再试。</p>
            <p>如有疑问，请联系客服：400-888-8888</p>
            <button onclick="location.reload()" class="retry-btn">重新加载</button>
        </div>
    `;
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .product-card {
        transition: all 0.3s ease;
    }
    
    .product-card.popular {
        position: relative;
        border: 2px solid #2c5aa0;
    }
    
    .popular-badge {
        position: absolute;
        top: -10px;
        right: 20px;
        background: #ff6b6b;
        color: white;
        padding: 5px 15px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .product-specs {
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    
    .spec-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
    }
    
    .spec-item span:first-child {
        font-weight: bold;
        color: #666;
    }
    
    .product-pricing {
        margin: 20px 0;
        text-align: center;
    }
    
    .price-option {
        margin: 10px 0;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        position: relative;
    }
    
    .price-option.recommended {
        border-color: #2c5aa0;
        background: #f0f7ff;
    }
    
    .price-label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
    }
    
    .price-value {
        display: block;
        font-size: 18px;
        font-weight: bold;
        color: #2c5aa0;
    }
    
    .price-save {
        position: absolute;
        top: -8px;
        right: 10px;
        background: #ff6b6b;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 10px;
    }
    
    .product-features {
        margin: 20px 0;
    }
    
    .product-features h4 {
        font-size: 14px;
        margin-bottom: 10px;
        color: #333;
    }
    
    .product-features ul {
        list-style: none;
        padding: 0;
    }
    
    .product-features li {
        padding: 3px 0;
        font-size: 13px;
        color: #666;
        position: relative;
        padding-left: 15px;
    }
    
    .product-features li:before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #28a745;
        font-weight: bold;
    }
    
    .product-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    
    .btn-purchase,
    .btn-compare {
        flex: 1;
        padding: 10px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-purchase {
        background: #2c5aa0;
        color: white;
    }
    
    .btn-purchase:hover {
        background: #1e3d6f;
    }
    
    .btn-compare {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #ddd;
    }
    
    .btn-compare:hover,
    .btn-compare.active {
        background: #2c5aa0;
        color: white;
        border-color: #2c5aa0;
    }
    
    .error-message {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .error-icon {
        font-size: 48px;
        margin-bottom: 20px;
    }
    
    .retry-btn {
        background: #2c5aa0;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 20px;
    }
    
    .retry-btn:hover {
        background: #1e3d6f;
    }
    
    .no-comparison {
        text-align: center;
        color: #666;
        padding: 40px;
        font-style: italic;
    }
    
    .comparison-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #ddd;
    }
    
    .clear-comparison {
        background: #dc3545;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
    }
    
    .clear-comparison:hover {
        background: #c82333;
    }
`;
document.head.appendChild(style);
