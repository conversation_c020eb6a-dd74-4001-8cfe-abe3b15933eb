// 联系页面专用JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initContactPage();
    initAdvancedContactForm();
    initMap();
});

// 初始化联系页面
function initContactPage() {
    // 添加页面特定的交互效果
    animateContactItems();
    initServiceHours();
}

// 联系项目动画
function animateContactItems() {
    const contactItems = document.querySelectorAll('.contact-item');
    
    contactItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

// 服务时间显示
function initServiceHours() {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // 判断是否在工作时间
    const isWorkingHours = (currentDay >= 1 && currentDay <= 5) && (currentHour >= 9 && currentHour < 18);
    
    // 更新服务状态显示
    const serviceStatus = document.createElement('div');
    serviceStatus.className = 'service-status';
    serviceStatus.innerHTML = `
        <div class="status-indicator ${isWorkingHours ? 'online' : 'offline'}">
            <span class="status-dot"></span>
            <span class="status-text">
                ${isWorkingHours ? '客服在线' : '客服离线'}
            </span>
        </div>
        <div class="next-service-time">
            ${getNextServiceTime(now)}
        </div>
    `;
    
    // 插入到联系信息区域
    const contactInfoSection = document.querySelector('.contact-info-section .container');
    if (contactInfoSection) {
        contactInfoSection.insertBefore(serviceStatus, contactInfoSection.firstChild);
    }
}

// 获取下次服务时间
function getNextServiceTime(now) {
    const currentHour = now.getHours();
    const currentDay = now.getDay();
    
    if (currentDay >= 1 && currentDay <= 5) {
        if (currentHour < 9) {
            return '今日 9:00 开始服务';
        } else if (currentHour >= 18) {
            return '明日 9:00 开始服务';
        } else {
            return '今日 18:00 结束服务';
        }
    } else {
        // 周末
        const daysUntilMonday = currentDay === 0 ? 1 : (8 - currentDay);
        return `周一 9:00 开始服务`;
    }
}

// 高级联系表单功能
function initAdvancedContactForm() {
    const form = document.getElementById('contact-form');
    if (!form) return;
    
    // 实时验证
    addRealTimeValidation(form);
    
    // 字符计数
    addCharacterCount(form);
    
    // 表单自动保存
    addAutoSave(form);
    
    // 提交处理
    form.addEventListener('submit', handleFormSubmit);
}

// 实时验证
function addRealTimeValidation(form) {
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

// 验证单个字段
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';
    
    // 清除之前的错误
    clearFieldError(field);
    
    switch (fieldName) {
        case 'name':
            if (!value) {
                errorMessage = '请输入姓名';
                isValid = false;
            } else if (value.length < 2) {
                errorMessage = '姓名至少需要2个字符';
                isValid = false;
            }
            break;
            
        case 'email':
            if (!value) {
                errorMessage = '请输入邮箱地址';
                isValid = false;
            } else if (!isValidEmail(value)) {
                errorMessage = '请输入有效的邮箱地址';
                isValid = false;
            }
            break;
            
        case 'phone':
            if (value && !isValidPhone(value)) {
                errorMessage = '请输入有效的手机号码';
                isValid = false;
            }
            break;
            
        case 'message':
            if (!value) {
                errorMessage = '请输入详细描述';
                isValid = false;
            } else if (value.length < 10) {
                errorMessage = '详细描述至少需要10个字符';
                isValid = false;
            }
            break;
    }
    
    if (!isValid) {
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

// 显示字段错误
function showFieldError(field, message) {
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;
    
    // 添加错误样式
    field.classList.add('error');
    
    // 创建错误消息
    let errorElement = formGroup.querySelector('.field-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        formGroup.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
}

// 清除字段错误
function clearFieldError(field) {
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;
    
    field.classList.remove('error');
    
    const errorElement = formGroup.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

// 验证手机号
function isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

// 字符计数
function addCharacterCount(form) {
    const textarea = form.querySelector('textarea[name="message"]');
    if (!textarea) return;
    
    const maxLength = 500;
    textarea.setAttribute('maxlength', maxLength);
    
    const counter = document.createElement('div');
    counter.className = 'character-counter';
    counter.innerHTML = `<span class="current">0</span>/<span class="max">${maxLength}</span>`;
    
    textarea.parentNode.appendChild(counter);
    
    textarea.addEventListener('input', function() {
        const current = this.value.length;
        counter.querySelector('.current').textContent = current;
        
        if (current > maxLength * 0.9) {
            counter.classList.add('warning');
        } else {
            counter.classList.remove('warning');
        }
    });
}

// 表单自动保存
function addAutoSave(form) {
    const inputs = form.querySelectorAll('input, textarea, select');
    const saveKey = 'contact_form_draft';
    
    // 加载保存的数据
    loadFormData(form, saveKey);
    
    // 监听输入变化
    inputs.forEach(input => {
        input.addEventListener('input', debounce(() => {
            saveFormData(form, saveKey);
        }, 1000));
    });
    
    // 表单提交成功后清除保存的数据
    form.addEventListener('submit', function() {
        localStorage.removeItem(saveKey);
    });
}

// 保存表单数据
function saveFormData(form, key) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    localStorage.setItem(key, JSON.stringify(data));
    
    // 显示保存提示
    showSaveIndicator();
}

// 加载表单数据
function loadFormData(form, key) {
    const savedData = localStorage.getItem(key);
    if (!savedData) return;
    
    try {
        const data = JSON.parse(savedData);
        Object.keys(data).forEach(name => {
            const field = form.querySelector(`[name="${name}"]`);
            if (field && data[name]) {
                field.value = data[name];
            }
        });
        
        // 显示恢复提示
        showRestoreNotification();
    } catch (e) {
        console.error('加载表单数据失败:', e);
    }
}

// 显示保存指示器
function showSaveIndicator() {
    let indicator = document.querySelector('.save-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'save-indicator';
        indicator.textContent = '草稿已保存';
        document.body.appendChild(indicator);
    }
    
    indicator.classList.add('show');
    setTimeout(() => {
        indicator.classList.remove('show');
    }, 2000);
}

// 显示恢复通知
function showRestoreNotification() {
    const notification = document.createElement('div');
    notification.className = 'restore-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span>检测到未完成的表单，已为您恢复</span>
            <button class="clear-draft">清除草稿</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 绑定清除草稿事件
    notification.querySelector('.clear-draft').addEventListener('click', function() {
        localStorage.removeItem('contact_form_draft');
        document.getElementById('contact-form').reset();
        notification.remove();
    });
    
    // 5秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 处理表单提交
function handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 验证所有字段
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // 检查协议同意
    const agreeCheckbox = form.querySelector('input[name="agree"]');
    if (agreeCheckbox && !agreeCheckbox.checked) {
        alert('请阅读并同意隐私政策和服务条款');
        isValid = false;
    }
    
    if (!isValid) {
        return;
    }
    
    // 提交表单
    submitContactForm(data, form);
}

// 提交联系表单
function submitContactForm(data, form) {
    const submitBtn = form.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    
    // 显示提交状态
    submitBtn.textContent = '提交中...';
    submitBtn.disabled = true;
    
    // 模拟API调用
    setTimeout(() => {
        // 这里应该调用实际的API
        console.log('提交的数据:', data);
        
        // 显示成功消息
        showSuccessMessage();
        
        // 重置表单
        form.reset();
        
        // 恢复按钮状态
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // 清除保存的草稿
        localStorage.removeItem('contact_form_draft');
        
    }, 2000);
}

// 显示成功消息
function showSuccessMessage() {
    const successModal = document.createElement('div');
    successModal.className = 'success-modal';
    successModal.innerHTML = `
        <div class="modal-content">
            <div class="success-icon">✓</div>
            <h3>提交成功</h3>
            <p>感谢您的咨询！我们已收到您的信息，会在24小时内与您联系。</p>
            <p>如有紧急问题，请直接拨打客服热线：<strong>400-888-8888</strong></p>
            <button class="close-modal">确定</button>
        </div>
    `;
    
    document.body.appendChild(successModal);
    
    // 绑定关闭事件
    successModal.querySelector('.close-modal').addEventListener('click', function() {
        successModal.remove();
    });
    
    // 点击背景关闭
    successModal.addEventListener('click', function(e) {
        if (e.target === successModal) {
            successModal.remove();
        }
    });
}

// 初始化地图
function initMap() {
    const mapContainer = document.getElementById('map');
    if (!mapContainer) return;
    
    // 这里应该集成实际的地图API（如百度地图、高德地图等）
    // 现在只是显示一个占位符
    mapContainer.innerHTML = `
        <div class="map-placeholder">
            <div class="map-info">
                <h4>公司位置</h4>
                <p>📍 北京市朝阳区科技园区幻方大厦18层</p>
                <p>🚇 地铁10号线科技园站A出口</p>
                <p>🚌 公交：108路、201路、305路</p>
                <button class="open-map" onclick="openExternalMap()">在地图中查看</button>
            </div>
        </div>
    `;
}

// 打开外部地图
function openExternalMap() {
    const address = '北京市朝阳区科技园区幻方大厦18层';
    const encodedAddress = encodeURIComponent(address);
    
    // 尝试打开不同的地图应用
    const mapUrls = [
        `https://uri.amap.com/marker?position=116.397428,39.90923&name=${encodedAddress}`, // 高德地图
        `https://map.baidu.com/?address=${encodedAddress}`, // 百度地图
        `https://maps.google.com/maps?q=${encodedAddress}` // Google地图
    ];
    
    // 在新窗口打开第一个可用的地图
    window.open(mapUrls[0], '_blank');
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 验证邮箱格式
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 添加联系页面专用样式
const contactStyles = document.createElement('style');
contactStyles.textContent = `
    .service-status {
        text-align: center;
        margin-bottom: 40px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .status-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }
    
    .status-indicator.online .status-dot {
        background: #28a745;
    }
    
    .status-indicator.offline .status-dot {
        background: #dc3545;
    }
    
    .status-text {
        font-weight: bold;
        font-size: 16px;
    }
    
    .status-indicator.online .status-text {
        color: #28a745;
    }
    
    .status-indicator.offline .status-text {
        color: #dc3545;
    }
    
    .next-service-time {
        color: #666;
        font-size: 14px;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .field-error {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }
    
    .form-group input.error,
    .form-group textarea.error,
    .form-group select.error {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    
    .character-counter {
        text-align: right;
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    
    .character-counter.warning {
        color: #ffc107;
    }
    
    .save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 8px 15px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        z-index: 1000;
    }
    
    .save-indicator.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .restore-notification {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #17a2b8;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 1000;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .clear-draft {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        padding: 5px 10px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
    }
    
    .clear-draft:hover {
        background: rgba(255,255,255,0.3);
    }
    
    .success-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }
    
    .modal-content {
        background: white;
        padding: 40px;
        border-radius: 10px;
        text-align: center;
        max-width: 400px;
        margin: 20px;
    }
    
    .success-icon {
        font-size: 48px;
        color: #28a745;
        margin-bottom: 20px;
    }
    
    .modal-content h3 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .modal-content p {
        color: #666;
        margin-bottom: 15px;
        line-height: 1.6;
    }
    
    .close-modal {
        background: #2c5aa0;
        color: white;
        border: none;
        padding: 10px 30px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        margin-top: 20px;
    }
    
    .close-modal:hover {
        background: #1e3d6f;
    }
    
    .map-placeholder {
        height: 300px;
        background: #f8f9fa;
        border: 2px dashed #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
    }
    
    .map-info {
        text-align: center;
        color: #666;
    }
    
    .map-info h4 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .map-info p {
        margin-bottom: 10px;
    }
    
    .open-map {
        background: #2c5aa0;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 15px;
    }
    
    .open-map:hover {
        background: #1e3d6f;
    }
    
    .checkbox-group {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }
    
    .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        cursor: pointer;
        font-size: 14px;
        line-height: 1.4;
    }
    
    .checkbox-label input[type="checkbox"] {
        margin: 0;
        width: auto;
    }
    
    .checkbox-label a {
        color: #2c5aa0;
        text-decoration: none;
    }
    
    .checkbox-label a:hover {
        text-decoration: underline;
    }
`;
document.head.appendChild(contactStyles);
