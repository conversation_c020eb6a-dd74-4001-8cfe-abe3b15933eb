// 网站配置文件
const CONFIG = {
    // 网站基本信息
    site: {
        name: '幻方清云',
        title: '幻方清云 - 专业云服务提供商',
        description: '幻方清云提供云服务器、虚拟主机、云应用、域名注册等专业云服务，7x24小时技术支持，99.9%稳定性保障。',
        keywords: '云服务器,虚拟主机,云应用,域名注册,IDC,云计算,幻方清云',
        author: '幻方清云技术团队',
        url: 'https://www.ly-y.cn',
        logo: 'images/logo/logo.png',
        favicon: 'images/logo/favicon.ico'
    },
    
    // 联系信息
    contact: {
        phone: '************',
        email: '<EMAIL>',
        address: '北京市朝阳区科技园区幻方大厦18层',
        workingHours: {
            weekdays: '周一至周五 9:00-18:00',
            weekend: '周末休息',
            emergency: '紧急技术支持 7x24小时'
        },
        social: {
            wechat: 'huanfang_cloud',
            qq: '800888888',
            weibo: '@幻方清云'
        }
    },
    
    // API配置
    api: {
        baseUrl: 'https://api.ly-y.cn',
        endpoints: {
            products: '/products.json',
            contact: '/contact',
            newsletter: '/newsletter',
            support: '/support'
        },
        timeout: 10000
    },
    
    // 其他域名配置
    domains: {
        www: 'https://www.ly-y.cn',
        auth: 'https://auth.ly-y.cn',
        app: 'https://app.ly-y.cn',
        api: 'https://api.ly-y.cn'
    },
    
    // 轮播图配置
    slider: {
        autoPlay: true,
        interval: 5000,
        showDots: true,
        showArrows: true,
        slides: [
            {
                id: 1,
                title: '专业云服务器',
                subtitle: '高性能、高可靠、弹性扩展',
                description: '提供多种配置的云服务器，满足不同业务需求，99.9%可用性保障',
                image: 'images/hero/slide1.jpg',
                ctaText: '立即体验',
                ctaLink: 'products.html#cloud-server'
            },
            {
                id: 2,
                title: '企业级虚拟主机',
                subtitle: '稳定快速、安全可靠',
                description: '专业虚拟主机服务，支持多种开发语言，一键部署网站应用',
                image: 'images/hero/slide2.jpg',
                ctaText: '了解详情',
                ctaLink: 'products.html#virtual-host'
            },
            {
                id: 3,
                title: '一站式云应用',
                subtitle: '开箱即用、快速部署',
                description: '提供WordPress、电商系统等热门应用，一键安装，快速上线',
                image: 'images/hero/slide3.jpg',
                ctaText: '查看应用',
                ctaLink: 'products.html#cloud-app'
            }
        ]
    },
    
    // 公司优势配置
    advantages: [
        {
            icon: 'icon-security',
            title: '安全可靠',
            description: '多重安全防护，数据加密传输，定期备份，确保业务安全稳定运行'
        },
        {
            icon: 'icon-performance',
            title: '高性能',
            description: '采用最新硬件设备，SSD存储，BGP多线接入，保障极速访问体验'
        },
        {
            icon: 'icon-support',
            title: '专业支持',
            description: '7x24小时技术支持，专业工程师团队，快速响应解决各类技术问题'
        },
        {
            icon: 'icon-flexible',
            title: '弹性扩展',
            description: '按需配置资源，支持弹性扩容，灵活应对业务增长需求'
        }
    ],
    
    // 公司统计数据
    stats: {
        customers: '10000+',
        servers: '5000+',
        uptime: '99.9%',
        support: '7x24'
    },
    
    // 导航菜单配置
    navigation: {
        main: [
            { name: '首页', url: 'index.html', active: true },
            { name: '产品服务', url: 'products.html' },
            { name: '关于我们', url: 'about.html' },
            { name: '联系我们', url: 'contact.html' }
        ],
        footer: {
            products: [
                { name: '云服务器', url: 'products.html#cloud-server' },
                { name: '虚拟主机', url: 'products.html#virtual-host' },
                { name: '云应用', url: 'products.html#cloud-app' },
                { name: '域名注册', url: 'products.html#domain' }
            ],
            company: [
                { name: '关于我们', url: 'about.html' },
                { name: '新闻动态', url: '#' },
                { name: '招聘信息', url: '#' },
                { name: '联系我们', url: 'contact.html' }
            ],
            support: [
                { name: '帮助中心', url: '#' },
                { name: '技术文档', url: '#' },
                { name: '服务条款', url: '#' },
                { name: '隐私政策', url: '#' }
            ]
        }
    },
    
    // SEO配置
    seo: {
        defaultTitle: '幻方清云 - 专业云服务提供商',
        titleSeparator: ' | ',
        defaultDescription: '幻方清云提供云服务器、虚拟主机、云应用、域名注册等专业云服务',
        defaultKeywords: '云服务器,虚拟主机,云应用,域名注册,IDC,云计算',
        ogImage: 'images/og-image.jpg',
        twitterCard: 'summary_large_image'
    },
    
    // 分析和追踪
    analytics: {
        googleAnalytics: '', // GA追踪ID
        baiduAnalytics: '', // 百度统计ID
        enableHeatmap: false, // 热力图
        enableChatbot: true // 在线客服
    },
    
    // 功能开关
    features: {
        newsletter: true, // 邮件订阅
        livechat: true, // 在线客服
        comparison: true, // 产品对比
        reviews: false, // 用户评价
        blog: false, // 博客功能
        search: false // 站内搜索
    },
    
    // 主题配置
    theme: {
        primaryColor: '#2c5aa0',
        secondaryColor: '#1e3d6f',
        accentColor: '#ff6b6b',
        successColor: '#28a745',
        warningColor: '#ffc107',
        errorColor: '#dc3545',
        fontFamily: "'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif"
    },
    
    // 缓存配置
    cache: {
        enableServiceWorker: false, // PWA支持
        cacheVersion: '1.0.0',
        staticCacheDuration: 86400, // 24小时
        apiCacheDuration: 300 // 5分钟
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// 全局配置对象
window.CONFIG = CONFIG;
